<template>
  <div style="margin-top: 12px; height: 100%; width: 100%; display: flex;flex-direction: column; position: relative">

    <div style="flex: 1; position: relative; border-radius: 8px; overflow: hidden">
      <a-spin :spinning="loading">
        <MapBox @onMapMounted="onMapMounted" />
      </a-spin>

      <MapStyle v-if="!!mapIns" v-show="false" :mapIns="mapIns" activeStyle="卫星图" ref="mapStyleRef" />
      <!-- 渠道图表 -->
      <div
        style="position: absolute; top: 4px; right: 4px;z-index: 1000; padding: 10px; height: 220px; width: 600px; background-color: #FFF;">
        <div
          style="position: absolute; top: 12px; left: 36%; width: 240px; height: 30px;z-index: 1000;font-size: 13px;">
          {{ currentTime }}
        </div>
        <div style="position: absolute; top: 6px; left: 60px; width: 120px; height: 25px;z-index: 1000;">
          <a-select v-model="hedaoName" allowClear style="width: 100%; height: 20px; font-weight: 400; font-size: 10px;"
            placeholder="请选择" :options="hedaoOptions" show-search></a-select>
        </div>
        <LineEchart :height="'210px'" :dataSource="lineChartData" :custom="lineChartCustom">
        </LineEchart>
      </div>
      <!-- 闸口图表 -->
      <div class="curve-panel" v-if="!!activeProcess">
        <div class="left">
          <div class="header">
            <div class="name">{{ activeProcess.projectName }}</div>
          </div>

          <div>
            <div class="indicator">
              <div class="label">上游水位:</div>
              <div class="value">{{ activeProcess.upWlv }}m</div>
            </div>
            <div class="indicator">
              <div class="label">下游水位:</div>
              <div class="value">{{ activeProcess.downWlv }}m</div>
            </div>
            <div class="indicator">
              <div class="label">过闸流量:</div>
              <div class="value">{{ activeProcess.q }}m³/s</div>
            </div>
          </div>

          <div style="text-align: center; margin-bottom: 10px">
            <a-button type="primary" size="small" @click.stop="activeProcess = null">收起曲线</a-button>
          </div>
        </div>
        <div class="right">
          <LineEchart :height="'210px'" :width="'100%'" :dataSource="lineChartDataShuiZha"
            :custom="lineChartCustomShuiZha">
          </LineEchart>
        </div>
      </div>
    </div>
    <div style="height: 30px; width: 100%; margin-top: -5px;">
      <TimePlaySlider v-if="times.length && !!mapIns" :times="times" @onTimeChange="onTimeChange" />
    </div>

  </div>
</template>

<script lang="jsx">
import MapBox from './MapBox/index.vue'
import MapStyle from './MapBox/MapStyle.vue'
import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
import { mapboxPopup } from './MapBox/popup.js'
import { mapBoundGeo } from '@/utils/mapBounds.js'
import { extractData, getChartsData } from './Utils.js'
import LineEchart from './Linechart/index.vue'
import axios from 'axios'
import modelData from './modelData.json'
import quDaoGeojson from './qudao.json'
import shuiza from './shuiza.json'
import district from './MapBox/district.json'

const showShuiZaLocation = {
  5667: [107.496450917882711, 41.015358164800972],
  5668: [107.496450917882711, 41.015358164800972],
  5669: [107.388045309515448, 40.807088663499151],
  5672: [107.388045309515448, 40.807088663499151],
  5673: [107.273417467873244, 40.676286607991244],
  5664: [107.496450917882711, 41.015358164800972],
  5665: [107.388045309515448, 40.807088663499151],
  5666: [107.273417467873244, 40.676286607991244]
}

function convertISODateToDateTime(isoDateStr) {
  // 创建 Date 对象解析 ISO 格式字符串
  const date = new Date(isoDateStr);
  // 辅助函数：补零
  const padZero = (num) => num.toString().padStart(2, '0');
  const year = date.getFullYear();
  const month = padZero(date.getMonth() + 1);
  const day = padZero(date.getDate());
  const hours = padZero(date.getHours());
  const minutes = padZero(date.getMinutes());
  const seconds = padZero(date.getSeconds());
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
export default {
  name: 'Simulation',
  props: ['dataSource', 'modelDataUrl'],
  components: { MapBox, MapStyle, TimePlaySlider, LineEchart },
  data() {
    return {
      mapIns: null,
      times: [],
      loading: false,
      currentTime: null,
      activeProcess: null,
      showPopupItem: [],
      hedaoName: '',
      hedaoOptions: [],
      lineChartData: [],
      lineChartCustom: {
        shortValue: true, // 缩写坐标值
        xLabel: '', // x轴名称
        yLabel: '水位(m)', //y轴名称
        yUnit: '', //y轴单位
        legend: true, // 图例
        showAreaStyle: true, // 颜色区域
        rYUnit: '', // 右侧y轴单位
        rYLabel: '流量(m³/s)', // 右侧y轴名称
        rYInverse: false, // 右侧y轴是否反向
        yNameLocation: "end",
        dataZoom: false,
        color: null,
        grid: {
          left: '1%',
          right: '1%',
          bottom: '5%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendLeft: '60%',
        xAxisData: []
      },
      lineChartCustomShuiZha: {
        shortValue: true, // 缩写坐标值
        xLabel: '', // x轴名称
        yLabel: '水位(m)', //y轴名称
        yUnit: '', //y轴单位
        legend: true, // 图例
        showAreaStyle: true, // 颜色区域
        rYUnit: '', // 右侧y轴单位
        rYLabel: '流量(m³/s)', // 右侧y轴名称
        rYInverse: false, // 右侧y轴是否反向
        yNameLocation: "end",
        dataZoom: false,
        color: null,
        grid: {
          left: '1%',
          right: '1%',
          bottom: '5%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendLeft: '40%',
        xAxisData: []
      },
      lineChartDataShuiZha: []
    }
  },
  watch: {
    hedaoName(newVal, oldVal) {
      if (newVal !== oldVal && this.currentTime) {
        this.refreshBottomChart(newVal)
      }
    },
    currentTime(newVal, oldVal) {
      if (newVal !== oldVal && this.hedaoName) {
        this.refreshBottomChart(this.hedaoName)
      }
    }
  },
  created() {
    // this.init(modelData)
  },
  mounted() {
    console.log(' this.modelDataUrl =', this.modelDataUrl)
    // this.onTimeChange(this.currentTime)
    // this.init(modelData)
    if (this.modelDataUrl) {
      this.loading = true
      console.log('this.modelDataUrl ------------------- ', `/downloads/${this.modelDataUrl}.json`)
      // 在开发和测试环境使用代理路径，在生产环境使用完整URL
      // const baseUrl = (process.env.NODE_ENV === 'development' || process.env.VUE_APP_ENV === 'test') ? '/downloads' : process.env.VUE_APP_GEOSERVER_BASE
      const baseUrl = `${process.env.VUE_APP_GEOSERVER_BASE}`
      axios.get(`${baseUrl}/${this.modelDataUrl}.json`, {
        timeout: 120 * 1000,
      }).then(res => {
        console.log('res ------------------- ', res)
        this.init(res.data)
      }).catch(err => {
        console.log('err ------------------- ', err)
        this.init(modelData)
      }).finally(() => {
        this.loading = false
      })
    }
  },
  methods: {
    init(modelData) {
      let { times, maxWlevel, maxQ, minWlevel, minQ } = extractData(modelData)
      this.times = times;
      this.currentTime = this.times[0]
      this.maxWlevel = maxWlevel;
      this.maxQ = maxQ;
      this.minWlevel = minWlevel;
      this.minQ = minQ;

      this.hedaoOptions = Object.keys(modelData).map(el => ({ label: el, value: el }))
      this.hedaoName = this.hedaoOptions[0].value
    },
    refreshBottomChart(newVal) {
      let { wlevel, q, stakes, maxWlevel, maxQ, minWlevel, minQ } = getChartsData(newVal, this.currentTime, modelData)
      let data3 = []
      let data1 = []
      let data2 = []
      stakes.forEach((element, index) => {
        data3.push([stakes[index]])
        data1.push(+(wlevel[index]))
        data2.push(+(q[index]))
      });
      let res = [{
        name: '水位(m)',
        color: '#507EF7',
        yAxisIndex: 0,
        data: data1
      },
      {
        name: '流量(m³/s)',
        color: '#B5E241',
        yAxisIndex: 1,
        data: data2
      }]
      this.lineChartCustom.xAxisData = data3
      this.lineChartCustom.yMax0 = maxWlevel
      this.lineChartCustom.yMin0 = minWlevel
      this.lineChartCustom.yMax1 = maxQ
      this.lineChartCustom.yMin1 = minQ
      this.lineChartData = res
    },
    onMapMounted(mapIns) {
      this.mapIns = mapIns
      this.$nextTick(() => {
        this.mapIns.resize()
        mapBoundGeo(district, this.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
        setTimeout(() => {
          this.mapIns.addLayer({
            id: 'diatrict',
            type: 'fill',
            source: {
              type: 'geojson',
              data: {
                type: 'Feature',
                geometry: {
                  type: 'Polygon',
                  coordinates: [
                    [
                      [-180, 90],
                      [180, 90],
                      [180, -90],
                      [-180, -90],
                    ],
                    district.features[0].geometry.coordinates[0][0],
                  ],
                },
              },
            },
            paint: { 'fill-color': 'rgba(255,255,255,0.4)' }, //{ 'fill-color': 'rgba(0,0,0,0.4)' },
            layout: { visibility: 'visible' },
          });
          this.mapIns.addLayer({
            id: 'shuiqu',
            type: 'line',
            source: {
              type: 'geojson',
              data: quDaoGeojson
            },
            paint: {
              "line-color": "#2BBCFF",
              "line-width": ['match', ['get', '渠道等'], '干渠', 4, '分干渠', 2, 1],
            },
            layout: { visibility: 'visible' },
          });
          this.mapIns.addLayer({
            id: 'shuiza',
            type: 'circle',
            source: {
              type: 'geojson',
              data: shuiza
            },
            paint: {
              "circle-radius": 4,
              "circle-color": 'white'
            },
            layout: { visibility: 'visible' },
          });

          let tempArr = []
          Object.keys(this.dataSource).forEach(el => {
            if (showShuiZaLocation[this.dataSource[el].projectId]) {
              tempArr.push({
                projectName: el,
                projectCode: this.dataSource[el].projectId,
                lngLat: showShuiZaLocation[this.dataSource[el].projectId],
                timeData: this.dataSource[el].timeData,
                q: this.dataSource[el].timeData[convertISODateToDateTime(this.currentTime)]?.q || 0,
                mapIns: this.mapIns
              })
            }
          })
          this.showPopupItem = tempArr
          this.showPopupItem.forEach(item => {
            this.dealPopup(item)
          })
        }, 1000)
      })
    },
    dealPopup(curr) {
      curr.q = curr.timeData[convertISODateToDateTime(this.currentTime)]?.q || 0
      const popupIns = mapboxPopup(this.mapIns, {
        ...curr,
        onPopupClose: item => {
          const index = this.showPopupItem.findIndex(el => el.projectCode === item.projectCode)
          this.showPopupItem[index].popupIns.remove()
          this.showPopupItem = this.showPopupItem.filter((el, i) => i !== index)
        },
        onProcessClick: item => {
          let times = Object.keys(item.timeData)
          let downWlv = []
          let q = []
          let upWlv = []
          times.forEach((element, index) => {
            downWlv.push(item.timeData[element].downWlv)
            q.push(+(item.timeData[element].q))
            upWlv.push(+(item.timeData[element].upWlv))
          });

          let res = [{
            name: '上游水位',
            color: '#507EF7',
            yAxisIndex: 0,
            data: upWlv
          },
          {
            name: '过闸流量',
            color: '#F7BA1E',
            yAxisIndex: 1,
            data: q
          },
          {
            name: '下游水位',
            color: '#B5E241',
            yAxisIndex: 0,
            data: downWlv
          }]
          this.lineChartCustomShuiZha.xAxisData = times
          this.lineChartCustom.yMax0 = Math.max(...upWlv, ...downWlv)
            this.lineChartCustom.yMin0 = Math.min(...upWlv, ...downWlv)
            this.lineChartCustom.yMax1 = Math.max(...q)
            this.lineChartCustom.yMin1 = Math.min(...q)
          this.lineChartDataShuiZha = res

          this.activeProcess = { ...item,downWlv:item.timeData[convertISODateToDateTime(this.currentTime)]?.downWlv || 0,upWlv:item.timeData[convertISODateToDateTime(this.currentTime)]?.upWlv || 0  }
        },
      })
      let index = this.showPopupItem.findIndex(el => el.projectCode == curr.projectCode)
      if (index == -1) {
        this.showPopupItem.push({ projectCode: curr.projectCode, popupIns })
      } else {
        this.showPopupItem[index]['popupIns'] = popupIns
      }
    },
    onTimeChange(time) {
      this.currentTime = time
      if (this.showPopupItem.length > 0) {
        this.handleOpenPopup()
      }
    },
    handleOpenPopup() {
      if (this.showPopupItem.length > 0 && !!this.mapIns) {
        this.showPopupItem.forEach(el => {
          el?.popupIns?.remove()
          this.dealPopup(this.showPopupItem.find(ele => ele.projectCode === el.projectCode))
        })
      }
    },
  },
}
</script>

<style lang="less" scoped>
.curve-panel {
  position: absolute;
  z-index: 1000;
  bottom: 4px;
  right: 4px;
  width: 700px;
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  background: #ffffff;

  .left {
    border-right: 1px solid #e5e6eb;
    width: 150px;
    position: relative;
    display: flex;
    flex-direction: column;

    .header {
      background: #f2f3f5;
      font-weight: 600;
      color: #1d2129;
      line-height: 20px;
      padding: 6px 8px;
      display: flex;
      align-items: center;

      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }

      .name {
        flex: 1;
        margin: 0 0 0 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .indicator {
      display: flex;
      padding: 6px 8px;
      justify-content: space-between;

      .label {
        color: '#4E5969';
      }

      .value {
        color: #1d2129;
      }
    }
  }

  .right {
    flex: 1;
    padding-top: 10px;
  }
}
</style>
